<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.battery.mapper.BatteryEverydayTotalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="batteryEverydayTotalResultMap" type="org.skyworth.ess.battery.entity.BatteryEverydayTotalEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="device_date_time" property="deviceDateTime"/>
        <result column="total_date" property="totalDate"/>
        <result column="battery_daily_charge_energy" property="batteryDailyChargeEnergy"/>
        <result column="battery_daily_discharge_energy" property="batteryDailyDischargeEnergy"/>
        <result column="battery_accumulated_charge_energy" property="batteryAccumulatedChargeEnergy"/>
        <result column="battery_accumulated_discharge_energy" property="batteryAccumulatedDischargeEnergy"/>
        <result column="throughput_use_rate" property="throughputUseRate"/>
        <result column="battery_maximum_cell_voltage" property="batteryMaximumCellVoltage"/>
        <result column="battery_minimum_cell_voltage" property="batteryMinimumCellVoltage"/>
        <result column="battery_maximum_cell_temperature" property="batteryMaximumCellTemperature"/>
        <result column="battery_minimum_cell_temperature" property="batteryMinimumCellTemperature"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="self_consumed" property="selfConsumed"/>
        <result column="fed_to_grid" property="fedToGrid"/>
        <result column="self_sufficiency" property="selfSufficiency"/>
        <result column="from_grid" property="fromGrid"/>
        <result column="l1_phase_voltage_of_ac_couple" property="l1PhaseVoltageOfAcCouple"/>
        <result column="l1_phase_current_of_ac_couple" property="l1PhaseCurrentOfAcCouple"/>
        <result column="l1_phase_power_of_ac_couple" property="l1PhasePowerOfAcCouple"/>
        <result column="l2_phase_voltage_of_ac_couple" property="l2PhaseVoltageOfAcCouple"/>
        <result column="l2_phase_current_of_ac_couple" property="l2PhaseCurrentOfAcCouple"/>
        <result column="l2_phase_power_of_ac_couple" property="l2PhasePowerOfAcCouple"/>
        <result column="l3_phase_voltage_of_ac_couple" property="l3PhaseVoltageOfAcCouple"/>
        <result column="l3_phase_current_of_ac_couple" property="l3PhaseCurrentOfAcCouple"/>
        <result column="l3_phase_power_of_ac_couple" property="l3PhasePowerOfAcCouple"/>
        <result column="frequency_of_ac_couple" property="frequencyOfAcCouple"/>
        <result column="energy_today_of_ac_couple_kwh" property="energyTodayOfAcCoupleKwh"/>
        <result column="energy_total_of_ac_couple" property="energyTotalOfAcCouple"/>
        <result column="energy_today_of_ac_couple_wh" property="energyTodayOfAcCoupleWh"/>
        <result column="pvl_daily_generating_energy_sum" property="pvlDailyGeneratingEnergySum"/>
        <result column="battery_daily_charge_energy_parallel" property="batteryDailyChargeEnergyParallel"/>
        <result column="battery_daily_discharge_energy_parallel" property="batteryDailyDischargeEnergyParallel"/>
        <result column="daily_energy_of_load_sum" property="dailyEnergyOfLoadSum"/>
        <result column="daily_support_energy_sum_to_backup" property="dailySupportEnergySumToBackup"/>
        <result column="today_export_energy" property="todayExportEnergy"/>
        <result column="today_import_energy" property="todayImportEnergy"/>
        <result column="parallel_self_consumed" property="parallelSelfConsumed"/>
        <result column="parallel_fed_to_grid" property="parallelFedToGrid"/>
        <result column="parallel_self_sufficiency" property="parallelSelfSufficiency"/>
        <result column="parallel_from_grid" property="parallelFromGrid"/>
        <result column="generator_today_energy_sum" property="generatorTodayEnergySum"/>
        <result column="battery_power_sum" property="batteryPowerSum"/>
    </resultMap>


    <select id="selectBatteryEverydayTotalPage" resultMap="batteryEverydayTotalResultMap">
        select *
        from battery_everyday_total
        where is_deleted = 0
    </select>


    <select id="exportBatteryEverydayTotal" resultType="org.skyworth.ess.battery.excel.BatteryEverydayTotalExcel">
        SELECT *
        FROM battery_everyday_total ${ew.customSqlSegment}
    </select>

    <select id="dailyEstimate" resultType="com.alibaba.fastjson.JSONObject">
        select
        DATE_FORMAT(CONVERT_TZ(device_date_time,'+00:00',#{queryCondition.timeZone}), '%Y-%m-%d') as totalDate,
        CAST(IFNULL(battery_daily_charge_energy / 1000, 0) AS DECIMAL (10, 2)) AS batteryDailyChargeEnergy,
        CAST(IFNULL(battery_daily_discharge_energy / 1000, 0) AS DECIMAL (10, 2)) AS batteryDailyDischargeEnergy,
        CAST(IFNULL(battery_accumulated_charge_energy / 1000, 0) AS DECIMAL (10, 2)) AS batteryAccumulatedChargeEnergy,
        CAST(IFNULL(battery_accumulated_discharge_energy / 1000, 0) AS DECIMAL (10, 2)) AS
        batteryAccumulatedDischargeEnergy
        from battery_everyday_total
        where is_deleted = 0 and plant_id = #{queryCondition.plantId}
        and device_serial_number=#{queryCondition.deviceSerialNumber}
        <if test="queryCondition.startDateTime != null">
            and device_date_time <![CDATA[ >= ]]>
            STR_TO_DATE(CONVERT_TZ(#{queryCondition.startDateTime},#{queryCondition.timeZone},'+00:00'),'%Y-%m-%d
            %H:%i:%s')
        </if>
        <if test="queryCondition.endDateTime != null">
            and device_date_time <![CDATA[ <= ]]>
            STR_TO_DATE(CONVERT_TZ(#{queryCondition.endDateTime},#{queryCondition.timeZone},'+00:00'),'%Y-%m-%d %H:%i:%s')
        </if>
        order BY total_date asc
    </select>

    <select id="dailyEstimateByMonth" resultType="com.alibaba.fastjson.JSONObject">
        select total_date as totalDate ,
        CAST(IFNULL(sum(battery_daily_charge_energy)/1000, 0) AS DECIMAL(10,2)) as batteryDailyChargeEnergy,
        CAST(IFNULL(sum(battery_daily_discharge_energy)/1000, 0) AS DECIMAL(10,2)) as batteryDailyDischargeEnergy,
        CAST(IFNULL(sum(battery_accumulated_charge_energy)/1000, 0) AS DECIMAL(10,2)) as batteryAccumulatedChargeEnergy,
        CAST(IFNULL(sum(battery_accumulated_discharge_energy)/1000, 0) AS DECIMAL(10,2)) as
        batteryAccumulatedDischargeEnergy
        from
        (
        select DATE_FORMAT(CONVERT_TZ(device_date_time,'+00:00',#{queryCondition.timeZone}), '%Y-%m') as total_date
        ,battery_daily_charge_energy, battery_daily_discharge_energy
        ,battery_accumulated_charge_energy,battery_accumulated_discharge_energy from battery_everyday_total
        where
        is_deleted = 0 and plant_id = #{queryCondition.plantId}
        and device_serial_number=#{queryCondition.deviceSerialNumber}
        <if test="queryCondition.startDateTime != null">
            and device_date_time <![CDATA[ >= ]]>
            STR_TO_DATE(CONVERT_TZ(#{queryCondition.startDateTime},#{queryCondition.timeZone},'+00:00'),'%Y-%m-%d
            %H:%i:%s')
        </if>
        <if test="queryCondition.endDateTime != null">
            and device_date_time <![CDATA[ <= ]]>
            STR_TO_DATE(CONVERT_TZ(#{queryCondition.endDateTime},#{queryCondition.timeZone},'+00:00'),'%Y-%m-%d %H:%i:%s')
        </if>
        ) as tmp group by total_date order by total_date asc
    </select>

    <select id="pieReport" resultMap="batteryEverydayTotalResultMap">
        select
        sum(b.self_consumed) as self_consumed,
        sum(b.fed_to_grid) as fed_to_grid,
        sum(b.self_sufficiency) as self_sufficiency,
        sum(b.from_grid) as from_grid
        from battery_everyday_total b
        where b.is_deleted =0 and b.plant_id = #{queryCondition.plantId}
        <if test="queryCondition.deviceSerialNumber != null">
            and b.device_serial_number=#{queryCondition.deviceSerialNumber}
        </if>
        <if test="queryCondition.startDateTime != null">
            <![CDATA[ and b.device_date_time  >= STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d %H:%i:%s')]]>
        </if>
        <if test="queryCondition.endDateTime != null">
            <![CDATA[ and b.device_date_time  <= STR_TO_DATE(#{queryCondition.endDateTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
    </select>

    <select id="queryEverydayTotalByDates" resultMap="batteryEverydayTotalResultMap">
        select total_date,
        battery_daily_charge_energy,
        battery_daily_discharge_energy,
        battery_accumulated_charge_energy,
        battery_accumulated_discharge_energy
        from battery_everyday_total
        where is_deleted = 0
        and plant_id = #{queryCondition.plantId}
        and device_serial_number = #{queryCondition.deviceSerialNumber}
        and total_date between #{queryCondition.startDateTime} and #{queryCondition.endDateTime}
        order by total_date asc
    </select>

    <select id="monthEstimate" resultType="org.skyworth.ess.battery.vo.BatteryEverydayTotalVO">
        select total_date as appTotalDate,sum(battery_daily_charge_energy) as
        appBatteryDailyChargeEnergy,sum(battery_daily_discharge_energy) as appBatteryDailyDischargeEnergy,
        sum(battery_accumulated_charge_energy) as appBatteryAccumulatedChargeEnergy
        from
        (select DATE_FORMAT(total_date,'%d') total_date,battery_daily_charge_energy,battery_daily_discharge_energy,
        battery_accumulated_charge_energy
        from battery_everyday_total
        where is_deleted = 0 and plant_id = #{queryCondition.plantId}
        <if test="queryCondition.deviceSerialNumber != null">
            and device_serial_number=#{queryCondition.deviceSerialNumber}
        </if>
        <if test="queryCondition.startDateTime != null">
            <![CDATA[ and DATE_FORMAT(total_date, '%Y-%m-%d') >= #{queryCondition.startDateTime} ]]>
        </if>
        <if test="queryCondition.endDateTime != null">
            <![CDATA[ and DATE_FORMAT(total_date, '%Y-%m-%d') <= #{queryCondition.endDateTime} ]]>
        </if>
        ) t
        group by total_date
        order by total_date asc
    </select>

    <select id="monthEstimateV2" resultType="org.skyworth.ess.battery.vo.BatteryEverydayTotalVO">
        select total_date as appTotalDate,
        appTodayEnergy as appTodayEnergy,
        appBatteryDailyDischargeEnergy as appBatteryDailyDischargeEnergy,
        appLoadAddEps as appLoadAddEps,
        appTodayImportEnergy as appTodayImportEnergy,
        appTodayExportEnergy as appTodayExportEnergy,
        appBatteryDailyChargeEnergy as appBatteryDailyChargeEnergy,
        appOtherPv as appOtherPv
        from
        (select b.device_date_time total_date,
        <include refid="weekMonthAnnualEstimateV2ColumnSql"/>
        from battery_everyday_total b inner join device_everyday_total d on b.plant_id =d.plant_id and
        b.device_serial_number =d.device_serial_number and d.is_deleted =0 and b.total_date = d.total_date
        where b.is_deleted = 0 and b.plant_id = #{queryCondition.plantId}
        <if test="queryCondition.deviceSerialNumber != null">
            and b.device_serial_number=#{queryCondition.deviceSerialNumber}
        </if>
        <if test="queryCondition.startDateTime != null">
            <![CDATA[ and b.device_date_time >= STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
        <if test="queryCondition.endDateTime != null">
            <![CDATA[ and b.device_date_time <= STR_TO_DATE(#{queryCondition.endDateTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
        ) t
        order by total_date asc
    </select>
    <sql id="weekMonthAnnualEstimateV2ColumnSql">
        ifnull(b.today_energy,0) as appTodayEnergy,
        ifnull(b.battery_daily_discharge_energy,0) as appBatteryDailyDischargeEnergy,
        ifnull(b.today_load_energy,0) + ifnull(b.daily_energy_to_eps,0) as appLoadAddEps,
        ifnull(d.today_import_energy ,0) as appTodayImportEnergy,
        ifnull(d.today_export_energy ,0) as appTodayExportEnergy,
        ifnull(b.battery_daily_charge_energy ,0) as appBatteryDailyChargeEnergy,
        ifnull (b.energy_today_of_ac_couple_wh,0) as appOtherPv
    </sql>
    <sql id="weekMonthAnnualEstimateV2ColumnSumSql">
        sum(appTodayEnergy) as appTodayEnergy,
        sum(appBatteryDailyDischargeEnergy) as appBatteryDailyDischargeEnergy,
        sum(appLoadAddEps) as appLoadAddEps,
        sum(appTodayImportEnergy) as appTodayImportEnergy,
        sum(appTodayExportEnergy) as appTodayExportEnergy,
        sum(appBatteryDailyChargeEnergy) as appBatteryDailyChargeEnergy,
        sum(appOtherPv) as appOtherPv
    </sql>
    <select id="annualEstimate" resultType="org.skyworth.ess.battery.vo.BatteryEverydayTotalVO">
        select total_date as appTotalDate,sum(battery_daily_charge_energy) as
        appBatteryDailyChargeEnergy,sum(battery_daily_discharge_energy) as appBatteryDailyDischargeEnergy ,
        sum(battery_accumulated_charge_energy) as appBatteryAccumulatedChargeEnergy
        from
        (select DATE_FORMAT(total_date,'%m') total_date,battery_daily_charge_energy,battery_daily_discharge_energy,
        battery_accumulated_charge_energy
        from battery_everyday_total
        where is_deleted = 0 and plant_id = #{queryCondition.plantId}
        <if test="queryCondition.deviceSerialNumber != null">
            and device_serial_number=#{queryCondition.deviceSerialNumber}
        </if>
        <if test="queryCondition.startDateTime != null">
            <![CDATA[ and DATE_FORMAT(total_date, '%Y-%m-%d') >= #{queryCondition.startDateTime} ]]>
        </if>
        <if test="queryCondition.endDateTime != null">
            <![CDATA[ and DATE_FORMAT(total_date, '%Y-%m-%d') <= #{queryCondition.endDateTime} ]]>
        </if>
        ) t
        group by total_date
        order by total_date asc
    </select>

    <select id="annualEstimateV2" resultType="org.skyworth.ess.battery.vo.BatteryEverydayTotalVO">
        select total_date as appTotalDate,
        <include refid="weekMonthAnnualEstimateV2ColumnSumSql"/>
        from
        (select
        DATE_FORMAT(CONVERT_TZ(b.device_date_time , '+00:00', #{queryCondition.timeZone}), '%m') total_date,
        <include refid="weekMonthAnnualEstimateV2ColumnSql"/>
        from battery_everyday_total b inner join device_everyday_total d on b.plant_id =d.plant_id and
        b.device_serial_number =d.device_serial_number and d.is_deleted =0 and b.total_date = d.total_date
        where b.is_deleted = 0 and b.plant_id = #{queryCondition.plantId}
        <if test="queryCondition.deviceSerialNumber != null">
            and b.device_serial_number=#{queryCondition.deviceSerialNumber}
        </if>
        <if test="queryCondition.startDateTime != null">
            <![CDATA[ and b.device_date_time >= STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
        <if test="queryCondition.endDateTime != null">
            <![CDATA[ and b.device_date_time <= STR_TO_DATE(#{queryCondition.endDateTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
        ) t
        group by total_date
        order by total_date asc
    </select>

    <select id="appReportEstimate" resultType="org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO">
        select id,device_date_time as appDeviceDateTime,DATE_FORMAT(device_date_time,'%H:%i') as appTotalDate,ifnull(
        pv_total_input_power,0) as appPvTotalInputPower,
        ifnull ( battery_power,0) as appBatteryPower,ifnull (phase_r_watt_of_load,0) as appPhaserWattOfLoad,
        ifnull ( phase_r_watt_of_eps,0) as appPhaserWattOfEps,
        ifnull (phase_r_watt_of_load,0) + ifnull ( phase_r_watt_of_eps,0) as appLoadAddEps
        from device_log22 dl where 1=1 and dl.device_date_time BETWEEN
        STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d %H:%i:%s')
        and STR_TO_DATE(#{queryCondition.endDateTime},'%Y-%m-%d %H:%i:%s')
        and dl.plant_id = #{queryCondition.plantId} and dl.device_serial_number = #{queryCondition.deviceSerialNumber}
        order by dl.device_date_time asc
    </select>

    <select id="weekEstimate" resultType="org.skyworth.ess.battery.vo.BatteryEverydayTotalVO">
        select total_date as appTotalDate,
        battery_daily_charge_energy as appBatteryDailyChargeEnergy,
        battery_daily_discharge_energy as appBatteryDailyDischargeEnergy,
        battery_accumulated_charge_energy as appBatteryAccumulatedChargeEnergy,
        battery_accumulated_discharge_energy
        from battery_everyday_total
        where is_deleted = 0 and plant_id = #{queryCondition.plantId}
        <if test="queryCondition.deviceSerialNumber != null">
            and device_serial_number=#{queryCondition.deviceSerialNumber}
        </if>
        <if test="queryCondition.startDateTime != null">
            <![CDATA[ and total_date >= #{queryCondition.startDateTime} ]]>
        </if>
        <if test="queryCondition.endDateTime != null">
            <![CDATA[ and total_date <= #{queryCondition.endDateTime} ]]>
        </if>
        order by total_date asc
    </select>

    <select id="weekEstimateV2" resultType="org.skyworth.ess.battery.vo.BatteryEverydayTotalVO">
        select b.device_date_time as appTotalDate,
        <include refid="weekMonthAnnualEstimateV2ColumnSql"/>
        from battery_everyday_total b inner join device_everyday_total d on b.plant_id =d.plant_id and
        b.device_serial_number =d.device_serial_number and d.is_deleted =0 and b.total_date = d.total_date
        where b.is_deleted = 0 and b.plant_id = #{queryCondition.plantId}
        <if test="queryCondition.deviceSerialNumber != null">
            and b.device_serial_number=#{queryCondition.deviceSerialNumber}
        </if>
        <if test="queryCondition.startDateTime != null">
            <![CDATA[ and b.device_date_time >= STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
        <if test="queryCondition.endDateTime != null">
            <![CDATA[ and b.device_date_time <= STR_TO_DATE(#{queryCondition.endDateTime},'%Y-%m-%d %H:%i:%s') ]]>
        </if>
        order by b.total_date asc
    </select>

    <select id="parallelPieReport" resultMap="batteryEverydayTotalResultMap">
        select
        sum(b.parallel_self_consumed) as parallel_self_consumed,
        sum(b.parallel_fed_to_grid) as parallel_fed_to_grid,
        sum(b.parallel_self_sufficiency) as parallel_self_sufficiency,
        sum(b.parallel_from_grid) as parallel_from_grid
        from battery_everyday_total b
        where b.is_deleted =0 and b.plant_id = #{queryCondition.plantId}
        <if test="queryCondition.deviceSerialNumber != null">
            and b.device_serial_number=#{queryCondition.deviceSerialNumber}
        </if>
        <if test="queryCondition.startDateTime != null">
            <![CDATA[ and b.total_date >= STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d')]]>
        </if>
        <if test="queryCondition.endDateTime != null">
            <![CDATA[ and b.total_date <= STR_TO_DATE(#{queryCondition.endDateTime},'%Y-%m-%d') ]]>
        </if>
    </select>

    <select id="parallelWeekEstimateV2" resultType="org.skyworth.ess.battery.vo.BatteryEverydayTotalVO">
        select b.total_date as appTotalDate,
        <include refid="parallelWeekMonthAnnualEstimateV2ColumnSql"/>
        <include refid="parallelEstimateV2Table"/>
        order by b.total_date asc
    </select>

    <select id="parallelMonthEstimateV2" resultType="org.skyworth.ess.battery.vo.BatteryEverydayTotalVO">
        select total_date as appTotalDate,
        <include refid="parallelWeekMonthAnnualEstimateV2ColumnSumSql"/>
        from
        (select DATE_FORMAT(b.total_date,'%d') total_date,
        <include refid="parallelWeekMonthAnnualEstimateV2ColumnSql"/>
        <include refid="parallelEstimateV2Table"/>
        ) t
        group by total_date
        order by total_date asc
    </select>

    <select id="parallelAnnualEstimateV2" resultType="org.skyworth.ess.battery.vo.BatteryEverydayTotalVO">
        select total_date as appTotalDate,
        <include refid="parallelWeekMonthAnnualEstimateV2ColumnSumSql"/>
        from
        (select DATE_FORMAT(b.total_date,'%m') total_date,
        <include refid="parallelWeekMonthAnnualEstimateV2ColumnSql"/>
        <include refid="parallelEstimateV2Table"/>
        ) t
        group by total_date
        order by total_date asc
    </select>

    <sql id="parallelEstimateV2Table">
        from battery_everyday_total b
        where b.is_deleted = 0 and b.plant_id = #{queryCondition.plantId}
        <if test="queryCondition.deviceSerialNumber != null">
            and b.device_serial_number=#{queryCondition.deviceSerialNumber}
        </if>
        <if test="queryCondition.startDateTime != null">
            <![CDATA[ and b.total_date >= STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d') ]]>
        </if>
        <if test="queryCondition.endDateTime != null">
            <![CDATA[ and b.total_date <= STR_TO_DATE(#{queryCondition.endDateTime},'%Y-%m-%d') ]]>
        </if>
    </sql>

    <sql id="parallelWeekMonthAnnualEstimateV2ColumnSql">
        ifnull(b.pvl_daily_generating_energy_sum,0) as appPvlDailyGeneratingEnergySum,
        ifnull(b.battery_daily_discharge_energy_parallel,0) as appBatteryDailyDischargeEnergyParallel,
        ifnull(b.daily_energy_of_load_sum,0) + ifnull(b.Daily_support_energy_sum_to_Backup,0) as appLoadAddBackup,
        ifnull(b.today_import_energy ,0) as appTodayImportEnergy,
        ifnull(b.today_export_energy ,0) as appTodayExportEnergy,
        ifnull(b.Battery_daily_charge_energy_parallel ,0) as appBatteryDailyChargeEnergyParallel
    </sql>

    <sql id="parallelWeekMonthAnnualEstimateV2ColumnSumSql">
        sum(appPvlDailyGeneratingEnergySum) as appPvlDailyGeneratingEnergySum,
        sum(appBatteryDailyDischargeEnergyParallel) as appBatteryDailyDischargeEnergyParallel,
        sum(appLoadAddBackup) as appLoadAddBackup,
        sum(appTodayImportEnergy) as appTodayImportEnergy,
        sum(appTodayExportEnergy) as appTodayExportEnergy,
        sum(appBatteryDailyChargeEnergyParallel) as appBatteryDailyChargeEnergyParallel
    </sql>
</mapper>
