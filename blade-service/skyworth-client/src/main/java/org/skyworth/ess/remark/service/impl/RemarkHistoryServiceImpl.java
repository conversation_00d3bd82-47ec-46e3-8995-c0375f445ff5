package org.skyworth.ess.remark.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.skyworth.ess.remark.entity.RemarkHistoryEntity;
import org.skyworth.ess.remark.vo.RemarkHistoryVO;
import org.skyworth.ess.remark.excel.RemarkHistoryExcel;
import org.skyworth.ess.remark.mapper.RemarkHistoryMapper;
import org.skyworth.ess.remark.service.IRemarkHistoryService;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.vo.BatchVO;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 站点评论记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Service
@RequiredArgsConstructor
public class RemarkHistoryServiceImpl extends BaseServiceImpl<RemarkHistoryMapper, RemarkHistoryEntity> implements IRemarkHistoryService {
	private final IAttachmentInfoClient attachmentInfoService;

	@Override
	public IPage<RemarkHistoryVO> selectRemarkHistoryPage(IPage<RemarkHistoryVO> page, RemarkHistoryVO remarkHistory) {
		return page.setRecords(baseMapper.selectRemarkHistoryPage(page, remarkHistory));
	}


	@Override
	public List<RemarkHistoryExcel> exportRemarkHistory(Wrapper<RemarkHistoryEntity> queryWrapper) {
		List<RemarkHistoryExcel> remarkHistoryList = baseMapper.exportRemarkHistory(queryWrapper);
		//remarkHistoryList.forEach(remarkHistory -> {
		//	remarkHistory.setTypeName(DictCache.getValue(DictEnum.YES_NO, RemarkHistory.getType()));
		//});
		return remarkHistoryList;
	}

	@Override
	public R saveRemarkAndPic(RemarkHistoryEntity remarkHistory) {
		// 业务验证
		if (remarkHistory.getPlantId() == null) {
			return R.fail("站点ID不能为空");
		}
		if (StringUtils.isNotEmpty(remarkHistory.getRemark()) && remarkHistory.getRemark().length() > 500) {
			return R.fail("评论不能超过500字");
		}

		BladeUser user = AuthUtil.getUser();
		String userNickName = Optional.ofNullable(user.getNickName())
			.filter(StringUtils::isNotBlank)
			.orElse(user.getUserName());
		remarkHistory.setRemarkUserName(userNickName);

		attachmentInfoService.saveAndUpdate(remarkHistory.getBatchVO());

		return R.status(super.save(remarkHistory));
	}

	@Override
	public IPage<RemarkHistoryEntity> pageListAndPic(Map<String, Object> remarkHistory, Query query) {
		// 分页查询评论记录
		IPage<RemarkHistoryEntity> pages = this.page(Condition.getPage(query), Condition.getQueryWrapper(remarkHistory, RemarkHistoryEntity.class));

		// 如果查询结果为空，直接返回
		if (pages.getTotal() == 0 || pages.getRecords().isEmpty()) {
			return pages;
		}

		List<Long> businessIds = pages.getRecords().stream()
			.map(RemarkHistoryEntity::getBusinessId)
			.filter(Objects::nonNull)
			.distinct()
			.collect(Collectors.toList());

		// 如果没有businessId，直接返回原始数据
		if (businessIds.isEmpty()) {
			return pages;
		}

		// 批量查询附件信息
		R<Map<Long, List<AttachmentInfoEntity>>> attachmentResult = attachmentInfoService.findByBusinessIds(businessIds);
		Map<Long, List<AttachmentInfoEntity>> attachmentMap = new HashMap<>();

		// 检查附件查询结果
		if (attachmentResult.isSuccess() && attachmentResult.getData() != null) {
			attachmentMap = attachmentResult.getData();
		}

		// 为每个评论记录设置对应的附件信息
		for (RemarkHistoryEntity record : pages.getRecords()) {
			if (record.getBusinessId() != null && attachmentMap.containsKey(record.getBusinessId())) {
				List<AttachmentInfoEntity> attachments = attachmentMap.get(record.getBusinessId());
				record.setAttachmentInfoEntity(attachments);
			}
		}

		return pages;
	}
}
