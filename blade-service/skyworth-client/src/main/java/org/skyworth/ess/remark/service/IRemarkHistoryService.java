package org.skyworth.ess.remark.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.skyworth.ess.remark.entity.RemarkHistoryEntity;
import org.skyworth.ess.remark.vo.RemarkHistoryVO;
import org.skyworth.ess.remark.excel.RemarkHistoryExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;

import java.util.List;
import java.util.Map;

/**
 * 站点评论记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
public interface IRemarkHistoryService extends BaseService<RemarkHistoryEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param remarkHistory
	 * @return
	 */
	IPage<RemarkHistoryVO> selectRemarkHistoryPage(IPage<RemarkHistoryVO> page, RemarkHistoryVO remarkHistory);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<RemarkHistoryExcel> exportRemarkHistory(Wrapper<RemarkHistoryEntity> queryWrapper);


	R saveRemarkAndPic(RemarkHistoryEntity entity);

	IPage<RemarkHistoryEntity> pageListAndPic(Map<String, Object> remarkHistory, Query query);
}
