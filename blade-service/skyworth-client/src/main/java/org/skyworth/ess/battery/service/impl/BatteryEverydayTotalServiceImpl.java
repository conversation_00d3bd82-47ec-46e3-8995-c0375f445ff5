/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.apache.commons.codec.Charsets;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity;
import org.skyworth.ess.battery.entity.BatteryEverydayTotalEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.excel.BatteryEverydayTotalExcel;
import org.skyworth.ess.battery.mapper.BatteryEverydayTotalMapper;
import org.skyworth.ess.battery.service.IBatteryCurrentStatusService;
import org.skyworth.ess.battery.service.IBatteryEverydayTotalService;
import org.skyworth.ess.battery.vo.BatteryCurrentStatusVO;
import org.skyworth.ess.battery.vo.BatteryEverydayTotalVO;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.util.EveryDayPowerAndEnergyEnum;
import org.skyworth.ess.util.TimeZoneUtil;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.EntityFieldConstant;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.DateUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 电池每日统计 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Service
@AllArgsConstructor
public class BatteryEverydayTotalServiceImpl extends BaseServiceImpl<BatteryEverydayTotalMapper,
	BatteryEverydayTotalEntity> implements IBatteryEverydayTotalService {

	private final IBatteryCurrentStatusService batteryCurrentStatusService;

	private final TimeZoneUtil timeZoneUtil;

	/**
	 * 电池每日统计
	 *
	 * @param queryCondition 入参
	 * @return List<BatteryEverydayTotalEntity>
	 * <AUTHOR>
	 * @since 2023/9/16 15:30
	 **/
	@Override
	public List<JSONObject> dailyEstimate(QueryCondition queryCondition) {
		Date startDateTime = queryCondition.getStartDateTime();
		Date endDateTime = queryCondition.getEndDateTime();
		Date currentDate = DateUtil.getCurrentDate();
		List<JSONObject> jsonObjects;
		String dateType = queryCondition.getQueryDateType();
		String timeZone = timeZoneUtil.getUserWebTimeZone();
		queryCondition.setTimeZone(timeZoneUtil.removeTimeZoneLetters(timeZone));
		if ("1".equals(dateType)) {
			jsonObjects = baseMapper.dailyEstimate(queryCondition);
			if (CollectionUtils.isNullOrEmpty(jsonObjects)) {
				jsonObjects = new ArrayList<>();
			}
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			try {
				startDateTime = sdf.parse(sdf.format(startDateTime));
				endDateTime = sdf.parse(sdf.format(endDateTime));

				// 如果包含今天，需要获取今天的数据
				if (startDateTime.compareTo(currentDate) <= 0 && endDateTime.compareTo(currentDate) >= 0) {
					// 获取今天的实时数据
					BatteryCurrentStatusEntity batteryCurrentStatus = new BatteryCurrentStatusVO();
					batteryCurrentStatus.setPlantId(queryCondition.getPlantId());
					batteryCurrentStatus.setDeviceSerialNumber(queryCondition.getDeviceSerialNumber());
					BatteryCurrentStatusEntity batteryCurrentStatusEntity =
						batteryCurrentStatusService.getOne(Condition.getQueryWrapper(batteryCurrentStatus));
					if (batteryCurrentStatusEntity != null) {
						JSONObject currentJsonObject = new JSONObject();
						// 设置日期为当前日期
						BigDecimal ten = new BigDecimal(1000);
						currentJsonObject.put(EntityFieldConstant.TOTAL_DATE, sdf.format(DateUtil.getCurrentDate()));
						currentJsonObject.put(EntityFieldConstant.BATTERY_DAILY_CHARGE_ENERGY,
							batteryCurrentStatusEntity.getBatteryDailyChargeEnergy().divide(ten, 2,
								RoundingMode.HALF_UP));
						currentJsonObject.put(EntityFieldConstant.BATTERY_DAILY_DISCHARGE_ENERGY,
							batteryCurrentStatusEntity.getBatteryDailyDischargeEnergy().divide(ten, 2,
								RoundingMode.HALF_UP));
						currentJsonObject.put(EntityFieldConstant.BATTERY_ACCUMULATED_CHARGE_ENERGY,
							batteryCurrentStatusEntity.getBatteryAccumulatedChargeEnergy().divide(ten, 2,
								RoundingMode.HALF_UP));
						currentJsonObject.put(EntityFieldConstant.BATTERY_ACCUMULATED_DISCHARGE_ENERGY,
							batteryCurrentStatusEntity.getBatteryAccumulatedDischargeEnergy().divide(ten, 2,
								RoundingMode.HALF_UP));
						jsonObjects.add(currentJsonObject);
					}
				}
			} catch (Exception e) {
				log.error(e.getMessage());
			}
			return dailyEstimateAbscissa(startDateTime, endDateTime, jsonObjects);
		} else {
			List<JSONObject> resultList = new ArrayList<>();
			jsonObjects = baseMapper.dailyEstimateByMonth(queryCondition);
			Map<String, JSONObject> collect =
				jsonObjects.stream().collect(Collectors.toMap(jsonObject -> jsonObject.getString(EntityFieldConstant.TOTAL_DATE),
					jsonObject -> jsonObject));

			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
			LocalDate sldt;
			LocalDate eldt;
			try {
				sldt =
					simpleDateFormat.parse(sdf.format(startDateTime)).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
				eldt =
					simpleDateFormat.parse(sdf.format(endDateTime)).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusMonths(1);
			} catch (ParseException e) {
				throw new RuntimeException(e);
			}
			//没有的则进行补
			while (!sldt.isEqual(eldt)) {
				String sldtString = sldt.format(formatter);
				if (collect.containsKey(sldtString)) {
					resultList.add(collect.get(sldtString));
				} else {
					JSONObject object = new JSONObject();
					object.put("batteryDailyChargeEnergy", "0");
					object.put("batteryDailyDischargeEnergy", "0");
					object.put("batteryAccumulatedChargeEnergy", "0");
					object.put("batteryAccumulatedDischargeEnergy", "0");
					object.put("totalDate", sldtString);
					resultList.add(object);
				}
				sldt = sldt.plusMonths(1);
			}
			if (resultList.isEmpty()) {
				return jsonObjects;
			} else {
				return resultList;
			}

		}

	}

	/**
	 * 封装参数不存在的时间段的空对象
	 *
	 * @param startDate   开始日期
	 * @param endDate     结束日期
	 * @param jsonObjects 入参
	 * @return List<BatteryEverydayTotalEntity>
	 * <AUTHOR>
	 * @since 2023/10/11 11:39
	 **/
	private List<JSONObject> dailyEstimateAbscissa(Date startDate, Date endDate, List<JSONObject> jsonObjects) {
		Set<Date> set =
			jsonObjects.stream().map(s -> s.getDate(EntityFieldConstant.TOTAL_DATE)).collect(Collectors.toSet());
		boolean flag = true;
		Date nextDay = startDate;
		while (flag) {
			if (!set.contains(nextDay)) {
				jsonObjects.add(buildBatteryEverydayJsonObject(nextDay));
			}
			if (nextDay.compareTo(endDate) == 0) {
				flag = false;
			} else {
				nextDay = DateUtil.plusDays(nextDay, 1);
			}
		}
		return jsonObjects.stream().sorted(Comparator.comparing(s -> s.getDate(EntityFieldConstant.TOTAL_DATE))).collect(Collectors.toList());
	}

	/**
	 * 创建空对象
	 *
	 * @param date 日期
	 * @return JSONObject
	 */
	private JSONObject buildBatteryEverydayJsonObject(Date date) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put(EntityFieldConstant.TOTAL_DATE, org.springblade.core.tool.utils.DateUtil.format(date,
			org.springblade.core.tool.utils.DateUtil.PATTERN_DATE));
		jsonObject.put(EntityFieldConstant.BATTERY_DAILY_CHARGE_ENERGY, BigDecimal.ZERO);
		jsonObject.put(EntityFieldConstant.BATTERY_DAILY_DISCHARGE_ENERGY, BigDecimal.ZERO);
		jsonObject.put(EntityFieldConstant.BATTERY_ACCUMULATED_CHARGE_ENERGY, BigDecimal.ZERO);
		jsonObject.put(EntityFieldConstant.BATTERY_ACCUMULATED_DISCHARGE_ENERGY, BigDecimal.ZERO);
		return jsonObject;
	}


	/**
	 * 电池总量统计
	 * 如果包含今天
	 *
	 * @param queryCondition 入参
	 * @return Map<String, Object>
	 * <AUTHOR>
	 * @since 2023/9/16 15:52
	 **/
	@Override
	public Map<String, Object> totalStatistics(QueryCondition queryCondition) {
		Map<String, Object> map = new HashMap<>(2);
		List<BatteryEverydayTotalEntity> batteryEverydayTotalEntityList =
			baseMapper.queryEverydayTotalByDates(queryCondition);
		// 数据计算(结束日期累计-开始日期累计=累计日期能量)
		if (!CollectionUtils.isNullOrEmpty(batteryEverydayTotalEntityList)) {
			calculateTotalStatistics(queryCondition, batteryEverydayTotalEntityList, map);
		} else {
			// 获取今天的实时数据
			BatteryCurrentStatusEntity batteryCurrentStatusEntity = getCurrentDateBatteryStatus(queryCondition);
			if (batteryCurrentStatusEntity != null) {
				BigDecimal divisor = new BigDecimal("1000");
				BigDecimal accumulatedChargeEnergy =
					batteryCurrentStatusEntity.getBatteryAccumulatedChargeEnergy().divide(divisor, 2,
						RoundingMode.HALF_UP);
				BigDecimal accumulatedDischargeEnergy =
					batteryCurrentStatusEntity.getBatteryAccumulatedDischargeEnergy().divide(divisor, 2,
						RoundingMode.HALF_UP);
				map.put("accumulatedChargeEnergy", accumulatedChargeEnergy);
				map.put("accumulatedDischargeEnergy", accumulatedDischargeEnergy);
			} else {
				map.put("accumulatedChargeEnergy", 0);
				map.put("accumulatedDischargeEnergy", 0);
			}
		}
		return map;
	}


	/**
	 * 数据总量统计
	 *
	 * @param queryCondition                 查询条件
	 * @param batteryEverydayTotalEntityList 每天的总量
	 * @param map                            入参
	 * <AUTHOR>
	 * @since 2023/9/18 12:03
	 **/
	private void calculateTotalStatistics(QueryCondition queryCondition,
										  List<BatteryEverydayTotalEntity> batteryEverydayTotalEntityList, Map<String,
		Object> map) {
		BigDecimal endAccumulatedChargeEnergy = BigDecimal.ZERO,
			endAccumulatedDischargeEnergy = BigDecimal.ZERO,
			startAccumulatedChargeEnergy = BigDecimal.ZERO,
			startAccumulatedDischargeEnergy = BigDecimal.ZERO,
			accumulatedChargeEnergy = BigDecimal.ZERO,
			accumulatedDischargeEnergy = BigDecimal.ZERO;
		Date startDateTime = queryCondition.getStartDateTime(), endDateTime = queryCondition.getEndDateTime();
		Date currentDate = DateUtil.getCurrentDate();
		int size = batteryEverydayTotalEntityList.size();
		// 获取今天的实时数据
		BatteryCurrentStatusEntity batteryCurrentStatusEntity = getCurrentDateBatteryStatus(queryCondition);
		// 统计时间范围内最小记录
		BatteryEverydayTotalEntity batteryEverydayTotalEntityBegin = batteryEverydayTotalEntityList.get(0);
		// 到统计的开始日期当天累计充电能量
		startAccumulatedChargeEnergy = batteryEverydayTotalEntityBegin.getBatteryAccumulatedChargeEnergy();
		// 到统计的开始日期当天累计放电能量
		startAccumulatedDischargeEnergy = batteryEverydayTotalEntityBegin.getBatteryAccumulatedDischargeEnergy();
		if (size > 1) {
			// 统计时间范围内最大记录
			BatteryEverydayTotalEntity batteryEverydayTotalEntityEnd = batteryEverydayTotalEntityList.get(size - 1);
			// 如果查询时间范围比今天小，则最大记录从查询结果里面获取
			if (startDateTime.compareTo(currentDate) < 0 && endDateTime.compareTo(currentDate) < 0) {
				endAccumulatedChargeEnergy = batteryEverydayTotalEntityEnd.getBatteryAccumulatedChargeEnergy();
				endAccumulatedDischargeEnergy = batteryEverydayTotalEntityEnd.getBatteryAccumulatedDischargeEnergy();
			}
			// 如果查询范围包含今天，则最大记录从当前状态获取
			else if (startDateTime.compareTo(currentDate) <= 0 && endDateTime.compareTo(currentDate) >= 0) {
				// 如果当前状态记录存在，则从当前记录获取
				if (batteryCurrentStatusEntity != null) {
					// 到今天累计充电能量
					endAccumulatedChargeEnergy = batteryCurrentStatusEntity.getBatteryAccumulatedChargeEnergy();
					// 到今天累计放电能量
					endAccumulatedDischargeEnergy = batteryCurrentStatusEntity.getBatteryAccumulatedDischargeEnergy();
				}
				// 如果当前状态记录不存在，主要解决设备数据是5分钟上报一次，今天的数据在跨天的几分钟内可能查不到，需要做额外处理
				else {
					endAccumulatedChargeEnergy = batteryEverydayTotalEntityEnd.getBatteryAccumulatedChargeEnergy();
					endAccumulatedDischargeEnergy =
						batteryEverydayTotalEntityEnd.getBatteryAccumulatedDischargeEnergy();
				}
			}
			// 计算这段时间累计充电量
			accumulatedChargeEnergy =
				nvlBigDecimal(endAccumulatedChargeEnergy).subtract(nvlBigDecimal(startAccumulatedChargeEnergy));
			// 计算这段时间累计放电量
			accumulatedDischargeEnergy =
				nvlBigDecimal(endAccumulatedDischargeEnergy).subtract(nvlBigDecimal(startAccumulatedDischargeEnergy));
		} else {
			// 如果当前状态记录存在，则从当前记录获取
			if (batteryCurrentStatusEntity != null) {
				// 到今天累计充电能量
				endAccumulatedChargeEnergy = batteryCurrentStatusEntity.getBatteryAccumulatedChargeEnergy();
				// 到今天累计放电能量
				endAccumulatedDischargeEnergy = batteryCurrentStatusEntity.getBatteryAccumulatedDischargeEnergy();
				accumulatedChargeEnergy =
					nvlBigDecimal(endAccumulatedChargeEnergy).subtract(nvlBigDecimal(startAccumulatedChargeEnergy));
				accumulatedDischargeEnergy =
					nvlBigDecimal(endAccumulatedDischargeEnergy).subtract(nvlBigDecimal(startAccumulatedDischargeEnergy));
			}
			// 当天数据是5分钟上报，今天的数据在这5分钟内可能查不到，需要做额外处理
			else {
				accumulatedChargeEnergy = startAccumulatedChargeEnergy;
				accumulatedDischargeEnergy = startAccumulatedDischargeEnergy;
			}
		}
		BigDecimal divisor = new BigDecimal("1000");
		BigDecimal accumulatedChargeEnergyResult = accumulatedChargeEnergy.divide(divisor, 2, RoundingMode.HALF_UP);
		BigDecimal accumulatedDischargeEnergyResult = accumulatedDischargeEnergy.divide(divisor, 2,
			RoundingMode.HALF_UP);
		map.put("accumulatedChargeEnergy", accumulatedChargeEnergyResult);
		map.put("accumulatedDischargeEnergy", accumulatedDischargeEnergyResult);
	}

	/**
	 * 根据站点id和逆变器SN，查询当天状态数据
	 *
	 * @param queryCondition 入参
	 * @return BatteryCurrentStatusEntity
	 * <AUTHOR>
	 * @since 2023/10/9 14:08
	 **/
	private BatteryCurrentStatusEntity getCurrentDateBatteryStatus(QueryCondition queryCondition) {
		BatteryCurrentStatusEntity batteryCurrentStatus = new BatteryCurrentStatusVO();
		batteryCurrentStatus.setPlantId(queryCondition.getPlantId());
		batteryCurrentStatus.setDeviceSerialNumber(queryCondition.getDeviceSerialNumber());
		return batteryCurrentStatusService.getOne(Condition.getQueryWrapper(batteryCurrentStatus));
	}

	/**
	 * 非空判断
	 *
	 * @param bigDecimal 入参
	 * @return BigDecimal
	 * <AUTHOR>
	 * @since 2023/9/18 18:54
	 **/
	private BigDecimal nvlBigDecimal(BigDecimal bigDecimal) {
		return Optional.ofNullable(bigDecimal).orElse(BigDecimal.ZERO);
	}

	@Override
	public IPage<BatteryEverydayTotalVO> selectBatteryEverydayTotalPage(IPage<BatteryEverydayTotalVO> page,
																		BatteryEverydayTotalVO batteryEverydayTotal) {
		return page.setRecords(baseMapper.selectBatteryEverydayTotalPage(page, batteryEverydayTotal));
	}


	@Override
	public List<BatteryEverydayTotalExcel> exportBatteryEverydayTotal(Wrapper<BatteryEverydayTotalEntity> queryWrapper) {
		return baseMapper.exportBatteryEverydayTotal(queryWrapper);
	}

	@Override
	public List<BatteryEverydayTotalVO> monthEstimate(QueryCondition queryCondition) {
		return baseMapper.monthEstimate(queryCondition);
	}

	@Override
	public List<BatteryEverydayTotalVO> monthEstimateV2(QueryCondition queryCondition) {
		// 时区转换
		// 将时间保留为 天的格式，便于后面和 初始化格式的数据（01，02,03） 比较，db中存在的 覆盖初始化中的数据
		List<BatteryEverydayTotalVO> batteryEverydayTotalVOList = baseMapper.monthEstimateV2(queryCondition);
		Optional.ofNullable(batteryEverydayTotalVOList).orElse(new ArrayList<>()).forEach(a -> {
			String appTotalDate = a.getAppTotalDate();
			a.setAppTotalDate(DateUtil.convertStringTimeZoneReturnString(appTotalDate,
				CommonConstant.COMMON_DEFAULT_TIME_ZONE, queryCondition.getTimeZone(), DateUtil.PATTERN_DAY));
		});

//		DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//		DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd");
//		batteryEverydayTotalVOList.forEach(a -> {
//			String appTotalDate = a.getAppTotalDate();
//			LocalDate date = LocalDate.parse(appTotalDate, inputFormatter);
//			String day = date.format(outputFormatter);
//			a.setAppTotalDate(day);
//		});
		return batteryEverydayTotalVOList;
	}

	@Override
	public List<BatteryEverydayTotalVO> annualEstimate(QueryCondition queryCondition) {
		return baseMapper.annualEstimate(queryCondition);
	}

	@Override
	public List<BatteryEverydayTotalVO> annualEstimateV2(QueryCondition queryCondition) {
		String timeZone = queryCondition.getTimeZone().toLowerCase();
		queryCondition.setTimeZone(timeZone.replace("utc", ""));
		return baseMapper.annualEstimateV2(queryCondition);
	}

	@Override
	public List<DeviceLog22VO> appReportEstimate(QueryDeviceLog22Condition queryCondition) {
		return baseMapper.appReportEstimate(queryCondition);
	}

	@Override
	public List<BatteryEverydayTotalVO> weekEstimate(QueryCondition queryCondition) {
		return baseMapper.weekEstimate(queryCondition);
	}

	@Override
	public List<BatteryEverydayTotalVO> weekEstimateV2(QueryCondition queryCondition) {
		// 时区转换
		// 将时间保留为 天的格式，便于后面和 初始化格式的数据（03-01，03-02,03-03） 比较，db中存在的 覆盖初始化中的数据
		List<BatteryEverydayTotalVO> batteryEverydayTotalVOList = baseMapper.weekEstimateV2(queryCondition);
		Optional.ofNullable(batteryEverydayTotalVOList).orElse(new ArrayList<>()).forEach(a -> {
			String appTotalDate = a.getAppTotalDate();
			a.setAppTotalDate(DateUtil.convertStringTimeZoneReturnString(appTotalDate,
				CommonConstant.COMMON_DEFAULT_TIME_ZONE, queryCondition.getTimeZone(), DateUtil.PATTERN_MONTH_DAY));
		});

//		DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//		DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("MM-dd");
//		batteryEverydayTotalVOList.forEach(a -> {
//			String appTotalDate = a.getAppTotalDate();
//			LocalDate date = LocalDate.parse(appTotalDate, inputFormatter);
//			String day = date.format(outputFormatter);
//			a.setAppTotalDate(day);
//		});
		return batteryEverydayTotalVOList;
	}

	@Override
	public void dailyEstimateExport(QueryCondition queryCondition, HttpServletResponse response) {
		List<JSONObject> jsonObjects = dailyEstimate(queryCondition);
		String queryDateType = queryCondition.getQueryDateType();
		Workbook workbook = new XSSFWorkbook();
		String language = CommonUtil.getCurrentLanguage();
		String sheetName = EveryDayPowerAndEnergyEnum.matchLanguage(language,
			EveryDayPowerAndEnergyEnum.battery.getCode());
		generateExcel(workbook, sheetName, jsonObjects, queryDateType);
		try {
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding(Charsets.UTF_8.name());
			String fileName = URLEncoder.encode(EveryDayPowerAndEnergyEnum.matchLanguage(language,
				EveryDayPowerAndEnergyEnum.Battery_Everyday_Energy.getCode()), Charsets.UTF_8.name());
			response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
			ServletOutputStream fos = response.getOutputStream();
			workbook.write(fos);
		} catch (IOException e) {
			log.error(e.getMessage());
		}
	}

	@Override
	public BatteryEverydayTotalEntity pieReport(QueryCondition queryCondition) {
		return baseMapper.pieReport(queryCondition);
	}

	@Override
	public BatteryEverydayTotalEntity parallelPieReport(QueryCondition queryCondition) {
		return baseMapper.parallelPieReport(queryCondition);
	}

	@Override
	public List<BatteryEverydayTotalVO> parallelWeekEstimateV2(QueryCondition queryCondition) {
		return baseMapper.parallelWeekEstimateV2(queryCondition);
	}

	@Override
	public List<BatteryEverydayTotalVO> parallelMonthEstimateV2(QueryCondition queryCondition) {
		return baseMapper.parallelMonthEstimateV2(queryCondition);
	}

	@Override
	public List<BatteryEverydayTotalVO> parallelAnnualEstimateV2(QueryCondition queryCondition) {
		return baseMapper.parallelAnnualEstimateV2(queryCondition);
	}

	public static void generateExcel(Workbook workbook, String sheetName, List<JSONObject> jsonObjects,
									 String queryDateType) {
		String language = CommonUtil.getCurrentLanguage();
		Sheet sheet = workbook.createSheet(sheetName);
		CellStyle verticalAlignment = workbook.createCellStyle();
		verticalAlignment.setVerticalAlignment(VerticalAlignment.CENTER);
		verticalAlignment.setAlignment(HorizontalAlignment.CENTER);
		int columnIndex = 0;
		sheet.setColumnWidth(columnIndex + 1, 256 * 15);
		sheet.setColumnWidth(columnIndex + 2, 256 * 50);
		sheet.setColumnWidth(columnIndex + 3, 256 * 50);
		Row headerRow1 = sheet.createRow(0);
		Row headerRow2 = sheet.createRow(1);
		Cell dateCell = headerRow1.createCell(columnIndex);
		dateCell.setCellValue(EveryDayPowerAndEnergyEnum.matchLanguage(language,
			EveryDayPowerAndEnergyEnum.Date.getCode()));
		dateCell.setCellStyle(verticalAlignment);

		Cell headerCell = headerRow1.createCell(columnIndex + 2);
		headerCell.setCellValue(EveryDayPowerAndEnergyEnum.matchLanguage(language,
			EveryDayPowerAndEnergyEnum.everydayEnergy.getCode()));
		headerCell.setCellStyle(verticalAlignment);
		// 在表头中插入新的单元格
		Cell headerCell1 = headerRow2.createCell(columnIndex + 2);
		Cell headerCell2 = headerRow2.createCell(columnIndex + 3);
		// 设置新单元格的值
		headerCell1.setCellValue(EveryDayPowerAndEnergyEnum.matchLanguage(language,
			EveryDayPowerAndEnergyEnum.batteryDailyChargeEnergy.getCode()));
		headerCell1.setCellStyle(verticalAlignment);
		headerCell2.setCellValue(EveryDayPowerAndEnergyEnum.matchLanguage(language,
			EveryDayPowerAndEnergyEnum.batteryDailyDischargeEnergy.getCode()));
		headerCell2.setCellStyle(verticalAlignment);
		// 合并单元格
		sheet.addMergedRegion(new CellRangeAddress(0, 0, columnIndex + 2, columnIndex + 3));
		sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 1));
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		//设置数据
		Map<String, JSONObject> collect;
		if (Constants.ONE.equals(queryDateType)) { //日
			collect = jsonObjects.stream()
				.collect(Collectors.toMap(
					jsonObject -> sdf.format(jsonObject.getDate(EntityFieldConstant.TOTAL_DATE)),
					jsonObject -> jsonObject,
					(existingValue, newValue) -> existingValue,
					LinkedHashMap::new
				));
			Map<String, List<JSONObject>> res = collect.entrySet().stream()
				.map(entry -> {
					JSONObject obj = entry.getValue();
					String yearMonth = entry.getKey().substring(0, 7);
					return new AbstractMap.SimpleEntry<>(yearMonth, obj);
				})
				.collect(Collectors.groupingBy(
					Map.Entry::getKey,
					Collectors.mapping(Map.Entry::getValue, Collectors.toList())
				));

			Map<String, List<JSONObject>> sortedRes = new TreeMap<>(res);

			int i = 1;
			for (Map.Entry<String, List<JSONObject>> entry : sortedRes.entrySet()) {
				String key = entry.getKey();
				List<JSONObject> objList = entry.getValue();
				int tmpI = i + 1;
				for (JSONObject obj : objList) {
					++i;
					Row dataRow = sheet.createRow(i);
					Cell cell1 = dataRow.createCell(columnIndex);
					verticalAlignment.setVerticalAlignment(VerticalAlignment.CENTER);
					verticalAlignment.setAlignment(HorizontalAlignment.CENTER);
					cell1.setCellStyle(verticalAlignment);
					cell1.setCellValue(key);

					Cell cell2 = dataRow.createCell(columnIndex + 1);
					cell2.setCellValue(sdf.format(obj.getDate(EntityFieldConstant.TOTAL_DATE)));
					cell2.setCellStyle(verticalAlignment);

					Cell cell3 = dataRow.createCell(columnIndex + 2);
					cell3.setCellValue(obj.getDoubleValue("batteryDailyChargeEnergy"));
					cell3.setCellStyle(verticalAlignment);

					Cell cell4 = dataRow.createCell(columnIndex + 3);
					cell4.setCellValue(obj.getDoubleValue("batteryDailyDischargeEnergy"));
					cell4.setCellStyle(verticalAlignment);
				}
				// 列数不一致则合并
				if (tmpI != i) {
					sheet.addMergedRegion(new CellRangeAddress(tmpI, i, 0, 0));
				}
			}

		} else {//月
			collect = jsonObjects.stream()
				.collect(Collectors.toMap(
					jsonObject -> jsonObject.getString(EntityFieldConstant.TOTAL_DATE),
					jsonObject -> jsonObject,
					(existingValue, newValue) -> existingValue,
					LinkedHashMap::new
				));
			int i = 1;
			for (Map.Entry<String, JSONObject> entry : collect.entrySet()) {
				String key = entry.getKey();
				JSONObject obj = entry.getValue();
				++i;
				Row dataRow = sheet.createRow(i);
				Cell cell1 = dataRow.createCell(columnIndex);
				verticalAlignment.setVerticalAlignment(VerticalAlignment.CENTER);
				verticalAlignment.setAlignment(HorizontalAlignment.CENTER);
				cell1.setCellStyle(verticalAlignment);
				cell1.setCellValue(key);

				Cell cell2 = dataRow.createCell(columnIndex + 1);
				cell2.setCellValue(key);
				cell2.setCellStyle(verticalAlignment);

				Cell cell3 = dataRow.createCell(columnIndex + 2);
				cell3.setCellValue(obj.getDoubleValue("batteryDailyChargeEnergy"));
				cell3.setCellStyle(verticalAlignment);

				Cell cell4 = dataRow.createCell(columnIndex + 3);
				cell4.setCellValue(obj.getDoubleValue("batteryDailyDischargeEnergy"));
				cell4.setCellStyle(verticalAlignment);
				sheet.addMergedRegion(new CellRangeAddress(i, i, 0, 1));
			}
		}

	}
}
